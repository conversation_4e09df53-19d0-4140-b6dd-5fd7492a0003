from tkinter import filedialog, messagebox
import threading
try:
    import Tkin<PERSON> as tk
except ImportError:
    import tkinter as tk

try:
    import ttk
    py3 = False
except ImportError:
    import tkinter.ttk as ttk
    py3 = True

import VideoSplitting_support
import subprocess
import os

def center_window(window, width, height):
    """Center the window on the screen."""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)
    window.geometry(f'{width}x{height}+{x}+{y}')

def vp_start_gui():
    global val, w, root
    root = tk.Tk()
    top = Toplevel1 (root)
    VideoSplitting_support.init(root, top)
    center_window(root, 700, 260)
    root.mainloop()

w = None
def create_Toplevel1(root, *args, **kwargs):
    global w, w_win, rt
    rt = root
    w = tk.Toplevel (root)
    top = Toplevel1 (w)
    VideoSplitting_support.init(w, top, *args, **kwargs)
    return (w, top)

def destroy_Toplevel1():
    global w
    w.destroy()
    w = None

class Toplevel1:
    def __init__(self, top=None):
        _bgcolor = '#d9d9d9'  # X11 color: 'gray85'
        _fgcolor = '#000000'  # X11 color: 'black'
        _compcolor = '#d9d9d9' # X11 color: 'gray85'
        _ana1color = '#d9d9d9' # X11 color: 'gray85'
        _ana2color = '#ececec' # Closest X11 color: 'gray92'

        top.geometry("700x260")
        top.title("Video Splitting")
        top.configure(background="#d9d9d9")
        top.configure(highlightbackground="#d9d9d9")
        top.configure(highlightcolor="black")

        self.Label1 = tk.Label(top)
        self.Label1.place(relx=0.033, rely=0.086, height=23, width=37)
        self.Label1.configure(activebackground="#f9f9f9")
        self.Label1.configure(activeforeground="black")
        self.Label1.configure(background="#d9d9d9")
        self.Label1.configure(disabledforeground="#a3a3a3")
        self.Label1.configure(foreground="#000000")
        self.Label1.configure(highlightbackground="#d9d9d9")
        self.Label1.configure(highlightcolor="black")
        self.Label1.configure(text='''路径''')

        self.Text1 = tk.Text(top)
        self.Text1.place(relx=0.133, rely=0.086, relheight=0.094, relwidth=0.823)
        self.Text1.configure(background="white")
        self.Text1.configure(font="TkTextFont")
        self.Text1.configure(foreground="black")
        self.Text1.configure(highlightbackground="#d9d9d9")
        self.Text1.configure(highlightcolor="black")
        self.Text1.configure(insertbackground="black")
        self.Text1.configure(selectbackground="#c4c4c4")
        self.Text1.configure(selectforeground="black")
        self.Text1.configure(width=494)
        self.Text1.configure(wrap='word')

        self.Button1 = tk.Button(top)
        self.Button1.place(relx=0.133, rely=0.258, height=28, width=75)
        self.Button1.configure(activebackground="#ececec")
        self.Button1.configure(activeforeground="#000000")
        self.Button1.configure(background="#d9d9d9")
        self.Button1.configure(disabledforeground="#a3a3a3")
        self.Button1.configure(foreground="#000000")
        self.Button1.configure(highlightbackground="#d9d9d9")
        self.Button1.configure(highlightcolor="black")
        self.Button1.configure(pady="0")
        self.Button1.configure(text='''读取文件''')
        self.Button1.config(command=self.load_file)

        self.Text2 = tk.Text(top)
        self.Text2.place(relx=0.133, rely=0.515, relheight=0.094, relwidth=0.157)
        self.Text2.configure(background="white")
        self.Text2.configure(font="TkTextFont")
        self.Text2.configure(foreground="black")
        self.Text2.configure(highlightbackground="#d9d9d9")
        self.Text2.configure(highlightcolor="black")
        self.Text2.configure(insertbackground="black")
        self.Text2.configure(selectbackground="#c4c4c4")
        self.Text2.configure(selectforeground="black")
        self.Text2.insert(tk.END, '00:00:00')  # Set default time
        self.Text2.configure(width=94)
        self.Text2.configure(wrap='word')

        self.Text3 = tk.Text(top)
        self.Text3.place(relx=0.4, rely=0.515, relheight=0.094, relwidth=0.157)
        self.Text3.configure(background="white")
        self.Text3.configure(font="TkTextFont")
        self.Text3.configure(foreground="black")
        self.Text3.configure(highlightbackground="#d9d9d9")
        self.Text3.configure(highlightcolor="black")
        self.Text3.configure(insertbackground="black")
        self.Text3.configure(selectbackground="#c4c4c4")
        self.Text3.configure(selectforeground="black")
        self.Text3.insert(tk.END, '00:00:00')  # Set default time
        self.Text3.configure(width=94)
        self.Text3.configure(wrap='word')

        self.Label2 = tk.Label(top)
        self.Label2.place(relx=0.033, rely=0.515, height=23, width=37)
        self.Label2.configure(activebackground="#f9f9f9")
        self.Label2.configure(activeforeground="black")
        self.Label2.configure(background="#d9d9d9")
        self.Label2.configure(disabledforeground="#a3a3a3")
        self.Label2.configure(foreground="#000000")
        self.Label2.configure(highlightbackground="#d9d9d9")
        self.Label2.configure(highlightcolor="black")
        self.Label2.configure(text='''开始''')

        self.Label3 = tk.Label(top)
        self.Label3.place(relx=0.317, rely=0.515, height=23, width=37)
        self.Label3.configure(activebackground="#f9f9f9")
        self.Label3.configure(activeforeground="black")
        self.Label3.configure(background="#d9d9d9")
        self.Label3.configure(disabledforeground="#a3a3a3")
        self.Label3.configure(foreground="#000000")
        self.Label3.configure(highlightbackground="#d9d9d9")
        self.Label3.configure(highlightcolor="black")
        self.Label3.configure(text='''结束''')

        self.Button2 = tk.Button(top)
        self.Button2.place(relx=0.133, rely=0.73, height=28, width=75)
        self.Button2.configure(activebackground="#ececec")
        self.Button2.configure(activeforeground="#000000")
        self.Button2.configure(background="#d9d9d9")
        self.Button2.configure(disabledforeground="#a3a3a3")
        self.Button2.configure(foreground="#000000")
        self.Button2.configure(highlightbackground="#d9d9d9")
        self.Button2.configure(highlightcolor="black")
        self.Button2.configure(pady="0")
        self.Button2.configure(text='''开始分割''')
        self.Button2.config(command=self.split_media)

        # 添加裁切模式选择
        self.Label4 = tk.Label(top)
        self.Label4.place(relx=0.6, rely=0.515, height=23, width=60)
        self.Label4.configure(activebackground="#f9f9f9")
        self.Label4.configure(activeforeground="black")
        self.Label4.configure(background="#d9d9d9")
        self.Label4.configure(disabledforeground="#a3a3a3")
        self.Label4.configure(foreground="#000000")
        self.Label4.configure(highlightbackground="#d9d9d9")
        self.Label4.configure(highlightcolor="black")
        self.Label4.configure(text='''裁切模式''')

        self.precise_mode = tk.BooleanVar()
        self.precise_mode.set(True)  # 默认使用精确模式
        self.Checkbutton1 = tk.Checkbutton(top)
        self.Checkbutton1.place(relx=0.75, rely=0.515, height=23, width=80)
        self.Checkbutton1.configure(activebackground="#ececec")
        self.Checkbutton1.configure(activeforeground="#000000")
        self.Checkbutton1.configure(background="#d9d9d9")
        self.Checkbutton1.configure(disabledforeground="#a3a3a3")
        self.Checkbutton1.configure(foreground="#000000")
        self.Checkbutton1.configure(highlightbackground="#d9d9d9")
        self.Checkbutton1.configure(highlightcolor="black")
        self.Checkbutton1.configure(text='''精确模式''')
        self.Checkbutton1.configure(variable=self.precise_mode)

        # 添加进度条
        self.progressbar = ttk.Progressbar(top)
        self.progressbar.place(relx=0.133, rely=0.85, relwidth=0.7, height=20)
        self.progressbar.configure(mode='indeterminate')
        self.progressbar.place_forget()  # 初始隐藏进度条

    def get_video_duration(self, file_path):
        '''获取视频时长'''
        try:
            command = f'ffprobe -v quiet -show_entries format=duration -of csv="p=0" "{file_path}"'
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                duration_seconds = float(result.stdout.strip())
                hours = int(duration_seconds // 3600)
                minutes = int((duration_seconds % 3600) // 60)
                seconds = int(duration_seconds % 60)
                return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except Exception as e:
            print(f"获取视频时长失败: {e}")
        return None

    def load_file(self):
        '''Load file and display its path in Text1'''
        filename = filedialog.askopenfilename()
        if filename:
            self.Text1.delete(1.0, tk.END)
            self.Text1.insert(tk.END, filename)

            # 自动获取视频时长并填写到时间框
            duration = self.get_video_duration(filename)
            if duration:
                # 设置开始时间为00:00:00
                self.Text2.delete(1.0, tk.END)
                self.Text2.insert(tk.END, '00:00:00')

                # 设置结束时间为视频总时长
                self.Text3.delete(1.0, tk.END)
                self.Text3.insert(tk.END, duration)

                messagebox.showinfo("视频信息", f"视频时长: {duration}\n已自动填写起始和结束时间\n\n裁切模式说明:\n• 精确模式: 使用GPU硬件加速重新编码，确保播放流畅\n  (自动尝试NVIDIA→AMD→Intel→CPU)\n• 快速模式: 直接复制，速度最快，但可能开始几秒画面静止")
            else:
                messagebox.showwarning("警告", "无法获取视频时长，请手动填写时间")

    def start_progress(self):
        '''显示并启动进度条'''
        self.progressbar.place(relx=0.133, rely=0.85, relwidth=0.7, height=20)
        self.progressbar.start(10)  # 每10ms更新一次

    def stop_progress(self):
        '''停止并隐藏进度条'''
        self.progressbar.stop()
        self.progressbar.place_forget()

    def split_media(self):
        '''Split media based on start and end times'''
        self.Button2.config(state=tk.DISABLED, text='分割中')  # 设置按钮为不可点击并更改文本
        self.start_progress()  # 显示进度条
        thread = threading.Thread(target=self._split_media)
        thread.start()

    def _split_media(self):
        '''Internal method to handle the actual media splitting process.'''
        file_path = self.Text1.get(1.0, tk.END).strip()
        start_time = self.Text2.get(1.0, tk.END).strip()
        end_time = self.Text3.get(1.0, tk.END).strip()
        if file_path and start_time and end_time:
            extension = os.path.splitext(file_path)[1].lower()
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_dir = os.path.dirname(file_path)

            # 根据模式选择不同的输出文件名和命令
            if self.precise_mode.get():
                output_file = os.path.join(output_dir, f"{base_name}_split_precise{extension}")
                # 精确模式：使用GPU硬件加速重新编码，确保关键帧正确
                # 优先级：NVIDIA GPU > AMD GPU > Intel GPU > CPU
                gpu_commands = [
                    # NVIDIA GPU
                    f'ffmpeg -hwaccel cuda -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_nvenc -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"',
                    # AMD GPU
                    f'ffmpeg -hwaccel d3d11va -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_amf -c:a aac -avoid_negative_ts make_zero "{output_file}"',
                    # Intel GPU
                    f'ffmpeg -hwaccel qsv -i "{file_path}" -ss {start_time} -to {end_time} -c:v h264_qsv -c:a aac -avoid_negative_ts make_zero "{output_file}"'
                ]
                # CPU备用命令
                fallback_command = f'ffmpeg -i "{file_path}" -ss {start_time} -to {end_time} -c:v libx264 -preset fast -c:a aac -avoid_negative_ts make_zero "{output_file}"'
                command = gpu_commands  # 传递GPU命令列表
            else:
                output_file = os.path.join(output_dir, f"{base_name}_split_fast{extension}")
                # 快速模式：流复制，速度快但可能有关键帧问题
                command = f'ffmpeg -ss {start_time} -i "{file_path}" -to {end_time} -c copy -avoid_negative_ts make_zero "{output_file}"'
                fallback_command = None

            # 执行命令
            result = None
            if self.precise_mode.get() and isinstance(command, list):
                # 精确模式：尝试GPU加速命令
                print("尝试GPU硬件加速...")
                gpu_types = ["NVIDIA", "AMD", "Intel"]

                for i, gpu_cmd in enumerate(command):
                    print(f"尝试{gpu_types[i]} GPU: {gpu_cmd}")
                    result = subprocess.run(gpu_cmd, shell=True, capture_output=True)
                    if result.returncode == 0:
                        print(f"{gpu_types[i]} GPU编码成功！")
                        break
                    else:
                        print(f"{gpu_types[i]} GPU编码失败")

                # 如果所有GPU都失败，使用CPU编码
                if result.returncode != 0 and fallback_command:
                    print("所有GPU编码失败，使用CPU编码...")
                    print(f"CPU命令: {fallback_command}")
                    result = subprocess.run(fallback_command, shell=True, capture_output=True)
            else:
                # 快速模式或单个命令
                cmd = command if isinstance(command, str) else command[0]
                print(f"使用{'精确' if self.precise_mode.get() else '快速'}模式: {cmd}")
                result = subprocess.run(cmd, shell=True, capture_output=True)

            if result.returncode == 0:
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                messagebox.showinfo("视频分割", "视频分割完成！")
            else:
                self.stop_progress()  # 停止进度条
                self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
                messagebox.showerror("错误",
                                     f"分割过程中发生错误。\n{result.stderr.decode('utf-8')}")
        else:
            self.stop_progress()  # 停止进度条
            self.Button2.config(state=tk.NORMAL, text='开始分割')  # 恢复按钮状态
            messagebox.showerror("错误", "请正确填写所有字段。")


if __name__ == '__main__':
    vp_start_gui()