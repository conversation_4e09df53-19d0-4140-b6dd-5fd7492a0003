// ==UserScript==
// @name         Show Right Clicked Element (Enhanced)
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  Logs the right-clicked DOM element to console with optimized performance
// <AUTHOR> by AI
// @match        *://*/*
// @grant        none
// ==/UserScript==

class ElementInspector {
    constructor() {
        this.selectorCache = new Map();
    }

    logElementInfo(element) {
        console.group('🎯 元素检查器');
        
        // 1. 打印元素基本信息
        console.log('右键点击的元素:', element);
        console.log('元素HTML:', element.outerHTML);
        
        // 2. 打印元素位置和尺寸
        const rect = element.getBoundingClientRect();
        console.group('📏 元素位置和尺寸');
        console.table({
            x: rect.x,
            y: rect.y,
            宽度: rect.width,
            高度: rect.height,
            左边: rect.left,
            顶部: rect.top,
            右边: rect.right,
            底部: rect.bottom
        });
        console.groupEnd();

        // 3. 生成选择器代码
        console.group('💻 JavaScript代码示例');
        
        // 3.1 获取最佳选择器
        const selector = this.getBestSelector(element);
        console.log('// 使用CSS选择器\n' + 
                   `const element = document.querySelector('${selector}');`);
        
        // 3.2 如果有ID，显示getElementById方法
        if (element.id) {
            console.log('\n// 使用ID选择\n' + 
                       `const elementById = document.getElementById('${element.id}');`);
        }
        
        // 3.3 显示XPath方法
        const xpath = this.getXPath(element);
        console.log('\n// 使用XPath选择\n' +
                   'function getElementByXPath(xpath) {\n' +
                   '    return document.evaluate(\n' +
                   '        xpath,\n' +
                   '        document,\n' +
                   '        null,\n' +
                   '        XPathResult.FIRST_ORDERED_NODE_TYPE,\n' +
                   '        null\n' +
                   '    ).singleNodeValue;\n' +
                   '}\n' +
                   `const elementByXPath = getElementByXPath('${xpath}');`);
        
        // 3.4 如果元素有文本内容，显示通过文本内容查找
        const text = element.textContent?.trim();
        if (text && text.length < 100) {
            console.log('\n// 通过文本内容查找\n' +
                       'const elementByText = Array.from(document.querySelectorAll("*"))\n' +
                       `    .find(el => el.textContent.trim() === "${text.replace(/"/g, '\\"')}");`);
        }
        
        // 3.5 显示完整的选择器信息
        console.log('\n📊 选择器对比:');
        console.log(`CSS选择器: ${selector}`);
        console.log(`XPath: ${xpath}`);
        console.log(`CSS长度: ${selector.length} 字符`);
        console.log(`XPath长度: ${xpath.length} 字符`);
        console.log(`推荐使用: ${selector.length <= xpath.length ? 'CSS选择器' : 'XPath'} (更短)`);
        
        console.groupEnd();
        
        // 4. 验证选择器
        try {
            const found = document.querySelector(selector);
            console.log(found === element ? 
                '✅ 选择器验证成功 - 可以准确定位元素' : 
                '⚠️ 选择器验证失败 - 可能不唯一');
        } catch (e) {
            console.log('❌ 选择器语法错误:', e.message);
        }
        
        console.groupEnd();
    }

    getBestSelector(element) {
        // 1. 尝试使用ID
        if (element.id) {
            return `#${CSS.escape(element.id)}`;
        }

        // 2. 获取所有属性并按优先级排序
        const attributes = Array.from(element.attributes);
        const tag = element.tagName.toLowerCase();
        
        // 2.1 优先使用data-*属性
        const dataAttrs = attributes.filter(attr => attr.name.startsWith('data-'));
        for (const attr of dataAttrs) {
            const selector = `${tag}[${attr.name}="${CSS.escape(attr.value)}"]`;
            if (this.isUniqueSelector(selector)) {
                return selector;
            }
        }

        // 2.2 尝试其他常用属性
        const commonAttrs = ['name', 'role', 'type', 'aria-label', 'title', 'placeholder'];
        for (const attrName of commonAttrs) {
            const value = element.getAttribute(attrName);
            if (value) {
                const selector = `${tag}[${attrName}="${CSS.escape(value)}"]`;
                if (this.isUniqueSelector(selector)) {
                    return selector;
                }
            }
        }

        // 2.3 尝试组合多个属性
        for (let i = 0; i < dataAttrs.length - 1; i++) {
            for (let j = i + 1; j < dataAttrs.length; j++) {
                const selector = `${tag}[${dataAttrs[i].name}="${CSS.escape(dataAttrs[i].value)}"][${dataAttrs[j].name}="${CSS.escape(dataAttrs[j].value)}"]`;
                if (this.isUniqueSelector(selector)) {
                    return selector;
                }
            }
        }

        // 3. 尝试使用类名
        if (element.className) {
            const classes = element.className.trim().split(/\s+/);
            const selector = `${tag}.${classes.map(c => CSS.escape(c)).join('.')}`;
            if (this.isUniqueSelector(selector)) {
                return selector;
            }
        }

        // 4. 使用路径选择器作为最后的备选
        return this.getPathSelector(element);
    }

    isUniqueSelector(selector) {
        if (this.selectorCache.has(selector)) {
            return this.selectorCache.get(selector);
        }
        try {
            const isUnique = document.querySelectorAll(selector).length === 1;
            this.selectorCache.set(selector, isUnique);
            return isUnique;
        } catch {
            return false;
        }
    }

    getPathSelector(element) {
        const path = [];
        let current = element;

        while (current && current !== document.body) {
            const tag = current.tagName.toLowerCase();
            const parent = current.parentElement;
            
            if (current.id) {
                path.unshift(`#${CSS.escape(current.id)}`);
                break;
            }
            
            if (parent) {
                const index = Array.from(parent.children)
                    .filter(e => e.tagName === current.tagName)
                    .indexOf(current) + 1;
                path.unshift(`${tag}:nth-of-type(${index})`);
            } else {
                path.unshift(tag);
            }
            
            current = parent;
        }

        return path.join(' > ');
    }

    getXPath(element) {
        if (element.id) {
            return `//*[@id="${element.id}"]`;
        }

        const parts = [];
        let current = element;

        while (current && current.nodeType === Node.ELEMENT_NODE) {
            const tag = current.nodeName.toLowerCase();
            const sameTagSiblings = Array.from(current.parentNode?.children || [])
                .filter(el => el.nodeName === current.nodeName);
            const index = sameTagSiblings.indexOf(current) + 1;
            parts.unshift(index > 1 ? `${tag}[${index}]` : tag);
            current = current.parentNode;
        }

        return '/' + parts.join('/');
    }
}

(function() {
    'use strict';
    
    const inspector = new ElementInspector();
    
    document.addEventListener('contextmenu', event => {
        // event.preventDefault(); // 取消注释可以禁用默认右键菜单
        inspector.logElementInfo(event.target);
    }, true);
})();
